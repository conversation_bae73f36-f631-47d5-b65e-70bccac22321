<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\SimpleFaceSwapService;
use Illuminate\Support\Facades\Log;
use Mockery;

class SimpleFaceSwapServiceTest extends TestCase
{
    /**
     * 测试createBatch方法在face_images为空时返回错误
     */
    public function test_create_batch_returns_error_when_face_images_empty()
    {
        // Mock Log facade to avoid actual logging during test
        Log::shouldReceive('error')->once();
        
        $service = new SimpleFaceSwapService();
        
        // 测试空数组
        $result = $service->createBatch(
            ['test_image.jpg'], // images
            [], // empty faceImages
            1, // userId
            false, // isPriority
            1 // totalTasks
        );
        
        $this->assertFalse($result['success']);
        $this->assertEquals('缺少face_images，无法创建换脸批次', $result['message']);
        $this->assertNull($result['batch_id']);
    }

    /**
     * 测试selectFaceImageForCharacter方法处理空face_images
     */
    public function test_select_face_image_for_character_handles_empty_face_images()
    {
        $service = new SimpleFaceSwapService();
        
        // 使用反射来测试私有方法
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('selectFaceImageForCharacter');
        $method->setAccessible(true);
        
        // 测试空face_images
        $result = $method->invoke($service, [], [1]);
        
        $this->assertEquals('', $result['image']);
        $this->assertEquals(0, $result['index']);
    }

    /**
     * 测试selectFaceImageForCharacter方法正常选择face_image
     */
    public function test_select_face_image_for_character_selects_correct_image()
    {
        $service = new SimpleFaceSwapService();
        
        // 使用反射来测试私有方法
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('selectFaceImageForCharacter');
        $method->setAccessible(true);
        
        $faceImages = ['face1.jpg', 'face2.jpg', 'face3.jpg'];
        
        // 测试选择第一个角色（索引0）
        $result = $method->invoke($service, $faceImages, [1]);
        $this->assertEquals('face1.jpg', $result['image']);
        $this->assertEquals(0, $result['index']);
        
        // 测试选择第二个角色（索引1）
        $result = $method->invoke($service, $faceImages, [2]);
        $this->assertEquals('face2.jpg', $result['image']);
        $this->assertEquals(1, $result['index']);
        
        // 测试选择超出范围的角色（应该选择最后一个）
        $result = $method->invoke($service, $faceImages, [5]);
        $this->assertEquals('face3.jpg', $result['image']);
        $this->assertEquals(2, $result['index']);
        
        // 测试空角色序列（应该选择第一个）
        $result = $method->invoke($service, $faceImages, []);
        $this->assertEquals('face1.jpg', $result['image']);
        $this->assertEquals(0, $result['index']);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
