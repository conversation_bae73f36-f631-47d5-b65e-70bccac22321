<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\OrderItem;
use App\Models\PicbookPreview;
use App\Jobs\ProcessOrderFaceSwap;
use App\Services\SimpleFaceSwapService;
use Mockery;
use ReflectionClass;
use ReflectionMethod;

class ProcessOrderFaceSwapJobTest extends TestCase
{
    /**
     * 测试从characters字段正确提取face_images的逻辑
     */
    public function test_face_images_extraction_logic_from_characters()
    {
        // 模拟预览数据 - face_images在characters中
        $previewData = [];
        $characters = [
            [
                'full_name' => 'Test Child',
                'language' => 'en',
                'gender' => 1,
                'skincolor' => 1,
                'photo' => 'test_face_image.jpg'
            ]
        ];
        $faceImageField = null;
        $language = 'en';
        $gender = 1;
        $skinColor = [1];
        
        // 执行提取逻辑
        $result = $this->extractFaceImagesLogic($previewData, $characters, $faceImageField, $language, $gender, $skinColor);
        
        // 验证结果
        $this->assertEquals(['test_face_image.jpg'], $result['faceImages']);
        $this->assertEquals('Test Child', $result['fullName']);
        $this->assertEquals('en', $result['language']);
        $this->assertEquals(1, $result['gender']);
        $this->assertEquals(1, $result['skincolor']);
    }

    /**
     * 测试从face_image字段提取face_images的逻辑（兼容旧数据）
     */
    public function test_face_images_extraction_logic_from_face_image_field()
    {
        // 模拟预览数据 - face_images在face_image字段中
        $previewData = [
            'full_name' => 'Legacy Child'
        ];
        $characters = []; // 空的characters
        $faceImageField = json_encode(['legacy_face_image.jpg']);
        $language = 'en';
        $gender = 1;
        $skinColor = [1];
        
        // 执行提取逻辑
        $result = $this->extractFaceImagesLogic($previewData, $characters, $faceImageField, $language, $gender, $skinColor);
        
        // 验证结果
        $this->assertEquals(['legacy_face_image.jpg'], $result['faceImages']);
        $this->assertEquals('Legacy Child', $result['fullName']);
        $this->assertEquals('en', $result['language']);
        $this->assertEquals(1, $result['gender']);
        $this->assertEquals(1, $result['skincolor']);
    }

    /**
     * 测试face_images为空时的逻辑
     */
    public function test_face_images_extraction_logic_when_empty()
    {
        // 模拟预览数据 - 没有face_images
        $previewData = [];
        $characters = []; // 空的characters
        $faceImageField = null; // 空的face_image
        $language = 'en';
        $gender = 1;
        $skinColor = [1];
        
        // 执行提取逻辑
        $result = $this->extractFaceImagesLogic($previewData, $characters, $faceImageField, $language, $gender, $skinColor);
        
        // 验证结果
        $this->assertEquals([], $result['faceImages']);
    }

    /**
     * 测试从face_image字段提取数组格式的face_images
     */
    public function test_face_images_extraction_logic_from_face_image_array()
    {
        // 模拟预览数据 - face_image是数组格式
        $previewData = [];
        $characters = []; // 空的characters
        $faceImageField = ['array_face_image.jpg', 'another_face.jpg'];
        $language = 'en';
        $gender = 1;
        $skinColor = [1];
        
        // 执行提取逻辑
        $result = $this->extractFaceImagesLogic($previewData, $characters, $faceImageField, $language, $gender, $skinColor);
        
        // 验证结果
        $this->assertEquals(['array_face_image.jpg', 'another_face.jpg'], $result['faceImages']);
    }

    /**
     * 模拟ProcessOrderFaceSwap job中的face_images提取逻辑
     */
    private function extractFaceImagesLogic($previewData, $characters, $faceImageField, $language, $gender, $skinColor)
    {
        // 提取face_images - 从characters数组中获取photo字段
        $faceImages = [];
        if (!empty($characters)) {
            foreach ($characters as $character) {
                if (isset($character['photo']) && !empty($character['photo'])) {
                    $faceImages[] = $character['photo'];
                }
            }
        }
        
        // 如果characters中没有找到，尝试从face_image字段获取（兼容旧数据）
        if (empty($faceImages) && !empty($faceImageField)) {
            // face_image可能是JSON字符串或数组
            if (is_string($faceImageField)) {
                $decodedFaceImage = json_decode($faceImageField, true);
                if (is_array($decodedFaceImage)) {
                    $faceImages = $decodedFaceImage;
                } else {
                    $faceImages = [$faceImageField];
                }
            } else if (is_array($faceImageField)) {
                $faceImages = $faceImageField;
            }
        }
        
        // 从characters或preview_data中获取其他参数
        $fullName = '';
        $extractedLanguage = 'en';
        $extractedGender = 1;
        $extractedSkincolor = 1;
        
        if (!empty($characters[0])) {
            $firstCharacter = $characters[0];
            $fullName = $firstCharacter['full_name'] ?? '';
            $extractedLanguage = $firstCharacter['language'] ?? 'en';
            $extractedGender = $firstCharacter['gender'] ?? 1;
            $extractedSkincolor = $firstCharacter['skincolor'] ?? 1;
        }
        
        // 如果characters中没有找到，从preview_data中获取（兼容性）
        if (empty($fullName)) {
            $fullName = $previewData['full_name'] ?? '';
        }
        if ($extractedLanguage === 'en') {
            $extractedLanguage = $previewData['language'] ?? $language ?? 'en';
        }
        if ($extractedGender === 1) {
            $extractedGender = $previewData['gender'] ?? $gender ?? 1;
        }
        if ($extractedSkincolor === 1) {
            $extractedSkincolor = $previewData['skincolor'] ?? ($skinColor[0] ?? 1);
        }
        
        return [
            'faceImages' => $faceImages,
            'fullName' => $fullName,
            'language' => $extractedLanguage,
            'gender' => $extractedGender,
            'skincolor' => $extractedSkincolor
        ];
    }
}
