<?php

namespace App\Jobs;

use App\Models\OrderItem;
use App\Services\SimpleFaceSwapService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessOrderFaceSwap implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $orderItemId;

    /**
     * Create a new job instance.
     */
    public function __construct(int $orderItemId)
    {
        $this->orderItemId = $orderItemId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $orderItem = OrderItem::findOrFail($this->orderItemId);
            $preview = $orderItem->preview;

            if (!$preview) {
                Log::error('订单项没有关联的预览', ['order_item_id' => $this->orderItemId]);
                return;
            }

            // 更新订单项状态为处理中
            $orderItem->setStatus(OrderItem::STATUS_PROCESSING);

            // 使用SimpleFaceSwapService创建全绘本批次
            $faceSwapService = app(SimpleFaceSwapService::class);
            
            // 从预览中获取必要的参数
            // preview_data 已经通过模型的 cast 转换为数组，无需 json_decode
            $previewData = $preview->preview_data ?? [];
            $characters = $preview->characters ?? [];

            // 提取face_images - 从characters数组中获取photo字段
            $faceImages = [];
            if (!empty($characters)) {
                foreach ($characters as $character) {
                    if (isset($character['photo']) && !empty($character['photo'])) {
                        $faceImages[] = $character['photo'];
                    }
                }
            }

            // 如果characters中没有找到，尝试从face_image字段获取（兼容旧数据）
            if (empty($faceImages) && !empty($preview->face_image)) {
                // face_image可能是JSON字符串或数组
                if (is_string($preview->face_image)) {
                    $decodedFaceImage = json_decode($preview->face_image, true);
                    if (is_array($decodedFaceImage)) {
                        $faceImages = $decodedFaceImage;
                    } else {
                        $faceImages = [$preview->face_image];
                    }
                } else if (is_array($preview->face_image)) {
                    $faceImages = $preview->face_image;
                }
            }

            // 从characters或preview_data中获取其他参数
            $fullName = '';
            $language = 'en';
            $gender = 1;
            $skincolor = 1;

            if (!empty($characters[0])) {
                $firstCharacter = $characters[0];
                $fullName = $firstCharacter['full_name'] ?? '';
                $language = $firstCharacter['language'] ?? 'en';
                $gender = $firstCharacter['gender'] ?? 1;
                $skincolor = $firstCharacter['skincolor'] ?? 1;
            }

            // 如果characters中没有找到，从preview_data中获取（兼容性）
            if (empty($fullName)) {
                $fullName = $previewData['full_name'] ?? '';
            }
            if ($language === 'en') {
                $language = $previewData['language'] ?? $preview->language ?? 'en';
            }
            if ($gender === 1) {
                $gender = $previewData['gender'] ?? $preview->gender ?? 1;
            }
            if ($skincolor === 1) {
                $skincolor = $previewData['skincolor'] ?? ($preview->skin_color[0] ?? 1);
            }

            Log::info('开始处理订单项的全绘本换脸', [
                'order_item_id' => $this->orderItemId,
                'order_id' => $orderItem->order_id,
                'preview_id' => $preview->id,
                'face_images_count' => count($faceImages),
                'face_images' => $faceImages,
                'full_name' => $fullName,
                'language' => $language,
                'gender' => $gender,
                'skincolor' => $skincolor
            ]);

            // 检查是否有face_images
            if (empty($faceImages)) {
                Log::error('订单项换脸失败：缺少face_image_url', [
                    'order_item_id' => $this->orderItemId,
                    'preview_id' => $preview->id,
                    'characters' => $characters,
                    'face_image_field' => $preview->face_image
                ]);
                throw new \Exception('缺少face_image_url，无法进行换脸处理');
            }

            $batchResult = $faceSwapService->createFullBookBatch(
                $preview->picbook_id,
                $faceImages,
                $fullName,
                $language,
                $gender,
                $skincolor,
                $orderItem->order_id,
                $preview->user_id
            );

            // 更新订单项的处理状态
            if (isset($batchResult['batch_id'])) {
                $orderItem->face_swap_batch_id = $batchResult['batch_id'];
                $orderItem->save();
                
                Log::info('订单项全绘本换脸批次创建成功', [
                    'order_item_id' => $this->orderItemId,
                    'batch_id' => $batchResult['batch_id']
                ]);
            } else {
                Log::error('创建全绘本换脸批次失败', [
                    'order_item_id' => $this->orderItemId,
                    'result' => $batchResult
                ]);
                throw new \Exception('创建全绘本换脸批次失败: ' . ($batchResult['message'] ?? '未知错误'));
            }

        } catch (\Exception $e) {
            Log::error('处理订单项换脸失败', [
                'order_item_id' => $this->orderItemId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 更新订单项状态为失败
            if (isset($orderItem)) {
                $orderItem->setStatus('failed');
            }

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('订单项换脸任务失败', [
            'order_item_id' => $this->orderItemId,
            'error' => $exception->getMessage()
        ]);

        try {
            $orderItem = OrderItem::find($this->orderItemId);
            if ($orderItem) {
                $orderItem->setStatus('failed');
            }
        } catch (\Exception $e) {
            Log::error('更新失败订单项状态时出错', [
                'order_item_id' => $this->orderItemId,
                'error' => $e->getMessage()
            ]);
        }
    }
}