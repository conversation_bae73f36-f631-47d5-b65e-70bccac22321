<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Cache;

use App\Models\AiFaceTask;
use App\Models\PicbookPage;
use App\Models\PicbookPageVariant;
use App\Models\PicbookPreview;
use App\Traits\ApiResponse;
use Illuminate\Support\Facades\DB;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class SimpleFaceSwapService
{
    use ApiResponse;

    /**
     * 缓存相关常量
     */
    const CACHE_PREFIX = 'simple_face_swap:';
    const CACHE_TTL = 3600; // 1小时

    protected $imageManager;

    public function __construct()
    {
        $this->imageManager = new ImageManager(new Driver());
    }



    /**
     * 创建换脸批次任务
     */
    public function createBatch(array $images, array $faceImages = [], ?int $userId = null, bool $isPriority = false, ?int $totalTasks = null)
    {
        try {
            // 验证必要参数
            if (empty($faceImages)) {
                Log::error('创建换脸批次失败：缺少face_images', [
                    'user_id' => $userId,
                    'images_count' => count($images),
                    'face_images' => $faceImages
                ]);
                return [
                    'success' => false,
                    'message' => '缺少face_images，无法创建换脸批次',
                    'batch_id' => null
                ];
            }

            // 生成批次ID
            $batchId = 'face_' . Str::uuid();

            // 开启数据库事务
            DB::beginTransaction();

            // 创建批次记录
            $batchRecord = AiFaceTask::create([
                'batch_id' => $batchId,
                'user_id' => $userId,
                'face_image_url' => $faceImages, // 直接存储数组，不手动编码
                'status' => 'pending',
                'type' => 'batch',
                'is_priority' => $isPriority,
                'total_tasks' => $totalTasks ?? count($images),
                'completed_tasks' => 0,
                'progress' => 0,
                'created_at' => now()
            ]);

            $taskIds = [];
            $taskIndex = 0;

            // 为每个图片创建任务记录
            foreach ($images as $image) {
                $imageUrl = is_array($image) ? ($image['image_url'] ?? $image['target_image_url'] ?? $image['url'] ?? '') : $image;
                $maskImageUrl = is_array($image) ? ($image['mask_image_url'] ?? null) : null;

                // 根据角色序列选择对应的face_image
                $characterSequence = $image['character_sequence'] ?? [];
                $selectedFaceImage = $this->selectFaceImageForCharacter($faceImages, $characterSequence);

                // 创建任务配置
                $config = [
                    'batch_id' => $batchId,
                    'task_index' => $taskIndex,
                    'character_sequence' => $characterSequence,
                    'selected_face_image_index' => $selectedFaceImage['index'] ?? 0
                ];

                // 如果是数组，提取页面ID和变体ID
                if (is_array($image)) {
                    $config['page_id'] = $image['page_id'] ?? null;
                    $config['variant_id'] = $image['variant_id'] ?? null;
                }

                // 创建任务记录
                $taskRecord = AiFaceTask::create([
                    'batch_id' => $batchId,
                    'user_id' => $userId,
                    'face_image_url' => [$selectedFaceImage['image']], // 直接存储数组
                    'target_image_url' => $imageUrl,
                    'mask_image' => $maskImageUrl,
                    'task_index' => $taskIndex,
                    'status' => 'pending',
                    'type' => 'task',
                    'is_priority' => $isPriority,
                    'page_id' => $config['page_id'] ?? null,
                    'variant_id' => $config['variant_id'] ?? null,
                    'character_sequence' => $characterSequence,
                    'total_tasks' => $totalTasks ?? count($images),
                    'completed_tasks' => 0,
                    'config' => $config
                ]);

                $taskIds[] = $taskRecord->id;
                $taskIndex++;

                Log::info('创建简化换脸任务', [
                    'batch_id' => $batchId,
                    'task_index' => $taskIndex - 1,
                    'task_id' => $taskRecord->id,
                    'mask_image' => $maskImageUrl,
                    'character_sequence' => $characterSequence,
                    'selected_face_image_index' => $selectedFaceImage['index'] ?? 0
                ]);
            }

            // 提交事务
            DB::commit();

            // 缓存批次信息
            Cache::put(self::CACHE_PREFIX . "batch:{$batchId}", [
                'total_tasks' => count($images),
                'user_id' => $userId,
                'face_images' => $faceImages, // 缓存多个face_image
                'status' => 'pending',
                'is_priority' => $isPriority,
                'created_at' => now()->toDateTimeString()
            ], now()->addHours(1));

            // 批次创建完成，等待调度器处理
            Log::info('批次创建成功，等待调度器处理', [
                'batch_id' => $batchId,
                'total_tasks' => count($images),
                'is_priority' => $isPriority,
                'face_images_count' => count($faceImages),
                'message' => '批次已创建，将由调度器按优先级和顺序处理'
            ]);

            // 广播批次已入队事件
            if ($batchRecord) {
                // 计算用户在队列中的位置
                $userId = $batchRecord->user_id;
                $isPriority = $batchRecord->is_priority;
                $batchCreatedAt = $batchRecord->created_at;
                
                // 获取用户前面的待处理任务数量
                $query = AiFaceTask::where('status', 'pending')
                    ->where('type', 'task')
                    ->where('created_at', '<', $batchCreatedAt);
                
                // 如果是高优先级任务，只计算高优先级队列中较早的任务
                // 如果是普通任务，计算所有高优先级任务 + 普通队列中较早的任务
                if ($isPriority) {
                    $query->where('is_priority', true);
                } else {
                    $query->where(function($q) use ($batchCreatedAt) {
                        $q->where('is_priority', true)
                          ->orWhere(function($q2) use ($batchCreatedAt) {
                              $q2->where('is_priority', false)
                                ->where('created_at', '<', $batchCreatedAt);
                          });
                    });
                }
                
                $queuePosition = $query->count();
                
                // 获取队列统计信息
                $regularQueueLength = AiFaceTask::where('status', 'pending')
                    ->where('type', 'task')
                    ->where('is_priority', false)
                    ->count();
                    
                $priorityQueueLength = AiFaceTask::where('status', 'pending')
                    ->where('type', 'task')
                    ->where('is_priority', true)
                    ->count();
                
                event(new \App\Events\AiFaceQueueStatus(
                    $userId,
                    $regularQueueLength,
                    $priorityQueueLength,
                    $isPriority ? 'high_priority' : 'regular',
                    $queuePosition,
                    $queuePosition * 120 // 估计等待时间，每个任务约2分钟
                ));
            }

            return [
                'success' => true,
                'batch_id' => $batchId,
                'task_ids' => $taskIds,
                'total_tasks' => count($images),
                'message' => '批次创建成功'
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('创建换脸批次失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => '创建批次失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 处理批次中的任务
     */
    public function processBatch(string $batchId)
    {
        try {
            // 使用数据库锁机制防止并发处理
            $lockAcquired = DB::transaction(function () use ($batchId) {
                // 检查当前批次状态，避免重复处理
                $currentBatch = AiFaceTask::where('batch_id', $batchId)
                    ->where('type', 'batch')
                    ->lockForUpdate()
                    ->first();

                if (!$currentBatch) {
                    throw new \Exception("批次不存在: {$batchId}");
                }

                if ($currentBatch->status !== 'processing') {
                    Log::info('批次状态不是processing，跳过处理', [
                        'batch_id' => $batchId,
                        'status' => $currentBatch->status
                    ]);
                    return false;
                }

                return true;
            });

            if (!$lockAcquired) {
                return [
                    'success' => true,
                    'message' => '批次已被处理或状态不正确'
                ];
            }

            // 更新批次状态为处理中
            AiFaceTask::where('batch_id', $batchId)
                ->where('type', 'batch')
                ->update(['status' => 'processing']);

            // 获取批次中的第一个待处理任务
            $nextTask = AiFaceTask::where('batch_id', $batchId)
                ->where('type', 'task')
                ->where('status', 'pending')
                ->orderBy('task_index')
                ->first();

            if ($nextTask) {
                Log::info('开始处理批次任务', [
                    'batch_id' => $batchId,
                    'task_id' => $nextTask->id,
                    'task_index' => $nextTask->task_index
                ]);

                // 处理该任务
                $result = $this->processTask($nextTask->id);

                // 检查是否有更多待处理任务
                $pendingCount = AiFaceTask::where('batch_id', $batchId)
                    ->where('type', 'task')
                    ->where('status', 'pending')
                    ->count();

                // 如果还有待处理任务，再次分派批次处理器
                if ($pendingCount > 0) {
                    // 检查是否是高优先级任务
                    $batchRecord = AiFaceTask::where('batch_id', $batchId)
                        ->where('type', 'batch')
                        ->first();

                    if ($batchRecord && $batchRecord->is_priority) {
                        \App\Jobs\ProcessHighPriorityFaceSwapBatch::dispatch($batchId)
                            ->delay(now()->addSeconds(3));
                    } else {
                        \App\Jobs\ProcessSimpleFaceSwapBatch::dispatch($batchId)
                            ->delay(now()->addSeconds(3));
                    }

                    Log::info('批次中还有待处理任务，已重新分派处理器', [
                        'batch_id' => $batchId,
                        'pending_tasks' => $pendingCount
                    ]);
                } else {
                    Log::info('批次所有任务已处理完成', [
                        'batch_id' => $batchId
                    ]);

                    // 更新批次状态为已完成
                    $this->updateBatchProgress($batchId);
                }

                return $result;
            } else {
                // 没有待处理任务，检查批次是否已完成
                $this->updateBatchProgress($batchId);

                return [
                    'success' => true,
                    'message' => '批次中没有待处理任务'
                ];
            }
        } catch (\Exception $e) {
            Log::error('处理批次任务失败', [
                'batch_id' => $batchId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => '处理批次任务失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 处理单个换脸任务
     */
    public function processTask(int $taskId)
    {
        try {
            // 获取任务记录
            $task = AiFaceTask::find($taskId);
            if (!$task) {
                throw new \Exception('任务不存在');
            }

            // 更新任务状态为处理中
            $task->status = 'processing';
            $task->save();
            Log::info('开始调用换脸API', [
                'task_id' => $taskId,
                'image_url' => $task->target_image_url,
                'face_image_url' => $task->face_image_url,
                'face_image_url_type' => gettype($task->face_image_url)
            ]);
            // 调用甲方换脸API
            $faceImageUrl = is_array($task->face_image_url) ? $task->face_image_url : json_decode($task->face_image_url, true);
            $result = $this->callFaceSwapApi($task->target_image_url, $faceImageUrl, $taskId);

            if ($result['success']) {
                // 处理成功，生成两种分辨率的图片
                $resultUrls = $this->generateResolutions($result['result_image_url']);

                // 更新任务状态和结果
                $task->status = 'completed';
                $task->result_image_url = $resultUrls['standard'];
                $task->result = [
                    'standard_url' => $resultUrls['standard'],
                    'high_res_url' => $resultUrls['high_res'],
                    'low_res_url' => $resultUrls['low_res']
                ];
                $task->completed_at = now();
                $task->save();

                // 更新批次进度
                $this->updateBatchProgress($task->batch_id);

                // 发送WebSocket通知
                $this->sendTaskCompletedNotification($task);

                // 更新picbook_previews表
                $this->updatePicbookPreview($task->batch_id, $task->status);

                return [
                    'success' => true,
                    'task_id' => $taskId,
                    'result_images' => $resultUrls,
                    'message' => '换脸任务处理成功'
                ];
            } else {
                // 处理失败
                $task->status = 'failed';
                $task->error_message = $result['error'] ?? '未知错误';
                $task->result = ['error' => $result['error']];
                $task->completed_at = now();
                $task->save();

                // 更新批次进度
                $this->updateBatchProgress($task->batch_id);

                // 发送失败WebSocket通知
                $this->sendTaskFailedNotification($task);

                // 更新picbook_previews表
                $this->updatePicbookPreview($task->batch_id, $task->status, $task->error_message);

                return [
                    'success' => false,
                    'task_id' => $taskId,
                    'error' => $result['error'],
                    'message' => '换脸任务处理失败'
                ];
            }
        } catch (\Exception $e) {
            Log::error('处理换脸任务失败', [
                'task_id' => $taskId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 更新任务状态为失败
            try {
                $task = AiFaceTask::find($taskId);
                if ($task) {
                    $task->status = 'failed';
                    $task->error_message = $e->getMessage();
                    $task->result = ['error' => $e->getMessage()];
                    $task->completed_at = now();
                    $task->save();

                    // 发送失败WebSocket通知
                    $this->sendTaskFailedNotification($task);

                    // 更新picbook_previews表
                    $this->updatePicbookPreview($task->batch_id, $task->status, $task->error_message);
                }
            } catch (\Exception $updateEx) {
                Log::error('更新任务状态失败', [
                    'task_id' => $taskId,
                    'error' => $updateEx->getMessage()
                ]);
            }

            return [
                'success' => false,
                'error' => '处理换脸任务失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 获取队列状态和预估时间
     */
    public function getQueueStatus(int $userId)
    {
        try {
            // 使用 QueueService 获取队列统计信息
            $queueService = app(\App\Services\QueueService::class);
            $stats = $queueService->getQueueStats($userId);

            // 获取用户当前的任务
            $userTasks = AiFaceTask::where('user_id', $userId)
                ->whereIn('status', ['pending', 'processing'])
                ->orderBy('created_at', 'desc')
                ->get();

            // 计算预估等待时间（每个任务平均处理时间约2分钟）
            $averageProcessingTime = 120; // 秒
            $estimatedWaitTime = ($stats['total_pending'] + $stats['total_processing']) * $averageProcessingTime;

            // 用户任务状态
            $userTasksStatus = $userTasks->map(function ($task) {
                return [
                    'batch_id' => $task->batch_id,
                    'status' => $task->status,
                    'progress' => $task->progress ?? 0,
                    'created_at' => $task->created_at,
                    'is_priority' => $task->is_priority
                ];
            });

            return [
                'success' => true,
                'queue_info' => [
                    'total_pending' => $stats['total_pending'],
                    'total_processing' => $stats['total_processing'],
                    'high_priority_pending' => $stats['high_priority_pending'],
                    'estimated_wait_time' => $estimatedWaitTime,
                    'estimated_wait_time_formatted' => $queueService->formatTime($estimatedWaitTime)
                ],
                'user_tasks' => $userTasksStatus,
                'user_tasks_count' => $userTasks->count()
            ];
        } catch (\Exception $e) {
            Log::error('获取队列状态失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 获取预览页面
     */
    public function getPreviewPages(int $picbookId, string $language, int $gender, int $skincolor)
    {
        try {
            // 获取绘本的预览页
            $previewPages = PicbookPage::where('picbook_id', $picbookId)
                ->where('is_preview', true)
                ->orderBy('page_number')
                ->get();

            if ($previewPages->isEmpty()) {
                throw new \Exception('未找到预览页面');
            }

            $pages = [];
            $faceSwapPages = [];
            $textMergePages = [];

            foreach ($previewPages as $page) {
                // 获取页面变体
                $variant = PicbookPageVariant::where('page_id', $page->id)
                    ->where('language', $language)
                    ->where('gender', $gender)
                    ->whereJsonContains('character_skincolors', [$skincolor])
                    ->first();

                if ($variant && !empty($variant->image_url)) {
                    $pageData = [
                        'page_id' => $page->id,
                        'page_number' => $page->page_number,
                        'has_question' => $page->has_question ?? false,
                        'has_choice' => $page->is_choices ?? false,
                        'choice_type' => $page->choice_type ?? 0,
                        'image_url' => $variant->image_url,
                        'content' => $variant->content,
                        'question' => $variant->question,
                        'choice_options' => $variant->choice_options,
                        'has_text' => $variant->has_text ?? false,
                        'has_face_swap' => !empty($variant->face_config) && !empty($variant->face_config['mask_url']),
                        'character_sequence' => $page->character_sequence ?? []
                    ];

                    $pages[] = $pageData;

                    // 分类页面类型
                    if ($pageData['has_face_swap']) {
                        $faceSwapPages[] = $pageData;
                    }
                    if ($pageData['has_text']) {
                        $textMergePages[] = $pageData;
                    }
                }
            }

            return [
                'success' => true,
                'total_pages' => count($pages),
                'face_swap_pages' => count($faceSwapPages),
                'text_merge_pages' => count($textMergePages),
                'pages' => $pages,
                'face_swap_page_list' => $faceSwapPages,
                'text_merge_page_list' => $textMergePages
            ];
        } catch (\Exception $e) {
            Log::error('获取预览页面失败', [
                'picbook_id' => $picbookId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 创建整本书的换脸任务（优化版：复用已有预览结果）
     */
    public function createFullBookBatch(int $picbookId, array $faceImages, string $fullName, string $language, int $gender, int $skincolor, int $orderId, int $userId)
    {
        try {
            // 获取绘本的所有页面（不仅仅是预览页）
            $allPages = PicbookPage::where('picbook_id', $picbookId)
                ->orderBy('page_number')
                ->get();

            if ($allPages->isEmpty()) {
                throw new \Exception('未找到绘本页面');
            }

            // 检查是否有已完成的预览结果可以复用
            $existingPreview = PicbookPreview::where('user_id', $userId)
                ->where('picbook_id', $picbookId)
                ->where('status', PicbookPreview::STATUS_COMPLETED)
                ->whereNotNull('result_images')
                ->orderBy('created_at', 'desc')
                ->first();

            $existingResults = [];
            $processedPageIds = [];

            if ($existingPreview && !empty($existingPreview->result_images)) {
                Log::info('发现可复用的预览结果', [
                    'preview_id' => $existingPreview->id,
                    'existing_results_count' => count($existingPreview->result_images)
                ]);

                // 提取已处理页面的结果
                foreach ($existingPreview->result_images as $result) {
                    if (isset($result['page_id']) && isset($result['result_image_url'])) {
                        $existingResults[$result['page_id']] = $result;
                        $processedPageIds[] = $result['page_id'];
                    }
                }
            }

            // 收集需要换脸的图片（跳过已处理的页面）
            $images = [];
            $totalTasks = 0;
            $skippedTasks = 0;

            foreach ($allPages as $page) {
                // 检查该页面是否已经处理过
                if (in_array($page->id, $processedPageIds)) {
                    $skippedTasks++;
                    Log::debug('跳过已处理的页面', [
                        'page_id' => $page->id,
                        'page_number' => $page->page_number
                    ]);
                    continue;
                }

                // 获取页面变体
                $variant = PicbookPageVariant::where('page_id', $page->id)
                    ->where('language', $language)
                    ->where('gender', $gender)
                    ->whereJsonContains('character_skincolors', [$skincolor])
                    ->first();

                if ($variant && !empty($variant->image_url)) {
                    // 检查是否需要换脸
                    if (!empty($variant->face_config) && !empty($variant->face_config['mask_url'])) {
                        $images[] = [
                            'page_id' => $page->id,
                            'variant_id' => $variant->id,
                            'target_image_url' => $variant->image_url,
                            'mask_image_url' => $variant->face_config['mask_url'],
                            'character_sequence' => $page->character_sequence ?? [],
                            'has_text' => $variant->has_text ?? false,
                            'text_elements' => $variant->text_elements ?? []
                        ];
                        $totalTasks++;
                    }
                }
            }

            Log::info('全书换脸任务分析', [
                'total_pages' => $allPages->count(),
                'need_processing' => $totalTasks,
                'can_reuse' => $skippedTasks,
                'optimization_rate' => $skippedTasks > 0 ? round(($skippedTasks / ($totalTasks + $skippedTasks)) * 100, 2) . '%' : '0%'
            ]);

            // 如果所有页面都已处理，直接返回成功并合并结果
            if (empty($images)) {
                if ($skippedTasks > 0) {
                    return $this->handleAllPagesAlreadyProcessed($existingResults, $orderId, $picbookId, $fullName, $language, $gender, $skincolor, $userId);
                } else {
                    throw new \Exception('未找到需要换脸的页面');
                }
            }

            // 创建高优先级换脸批次（整本书任务优先级更高）
            $result = $this->createBatch($images, $faceImages, $userId, true, $totalTasks);

            if ($result['success']) {
                // 记录订单关联信息，包括已有结果
                $config = [
                    'order_id' => $orderId,
                    'picbook_id' => $picbookId,
                    'full_name' => $fullName,
                    'language' => $language,
                    'gender' => $gender,
                    'skincolor' => $skincolor,
                    'type' => 'full_book'
                ];

                // 如果有已存在的结果，记录下来以便后续合并
                if (!empty($existingResults)) {
                    $config['existing_results'] = $existingResults;
                    $config['reused_pages_count'] = count($existingResults);
                }

                AiFaceTask::where('batch_id', $result['batch_id'])
                    ->where('type', 'batch')
                    ->update(['config' => $config]);

                Log::info('整本书换脸任务创建成功', [
                    'batch_id' => $result['batch_id'],
                    'order_id' => $orderId,
                    'new_tasks' => $totalTasks,
                    'reused_results' => count($existingResults),
                    'total_pages' => $totalTasks + count($existingResults),
                    'face_images_count' => count($faceImages)
                ]);
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('创建整本书换脸任务失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 处理所有页面都已完成的情况
     */
    private function handleAllPagesAlreadyProcessed($existingResults, $orderId, $picbookId, $fullName, $language, $gender, $skincolor, $userId)
    {
        try {
            // 生成一个虚拟的批次ID用于标识
            $batchId = 'reuse_' . Str::uuid();

            Log::info('所有页面都已处理，直接复用结果', [
                'batch_id' => $batchId,
                'order_id' => $orderId,
                'reused_pages_count' => count($existingResults)
            ]);

            // 创建一个批次记录，标记为已完成
            AiFaceTask::create([
                'batch_id' => $batchId,
                'user_id' => $userId,
                'status' => 'completed',
                'type' => 'batch',
                'is_priority' => true,
                'total_tasks' => count($existingResults),
                'completed_tasks' => count($existingResults),
                'progress' => 100,
                'config' => [
                    'order_id' => $orderId,
                    'picbook_id' => $picbookId,
                    'full_name' => $fullName,
                    'language' => $language,
                    'gender' => $gender,
                    'skincolor' => $skincolor,
                    'type' => 'full_book_reuse',
                    'existing_results' => $existingResults,
                    'reused_pages_count' => count($existingResults)
                ],
                'completed_at' => now()
            ]);

            // 直接更新订单项数据
            $this->updateOrderItemWithReusedResults($orderId, $batchId, $existingResults);

            return [
                'success' => true,
                'batch_id' => $batchId,
                'message' => '所有页面都已处理，直接复用现有结果',
                'reused_pages_count' => count($existingResults),
                'total_tasks' => count($existingResults)
            ];

        } catch (\Exception $e) {
            Log::error('处理复用结果失败', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => '处理复用结果失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 使用复用结果更新订单项
     */
    private function updateOrderItemWithReusedResults($orderId, $batchId, $existingResults)
    {
        try {
            $orderItem = \App\Models\OrderItem::where('order_id', $orderId)->first();

            if ($orderItem) {
                // 构建页面数据
                $pages = [];
                $resultImages = [];

                foreach ($existingResults as $result) {
                    $pageNumber = $this->getPageNumber($result['page_id']);

                    $resultImages[] = [
                        'page_id' => $result['page_id'],
                        'variant_id' => $result['variant_id'] ?? null,
                        'page_number' => $pageNumber,
                        'result_image_url' => $result['result_image_url'],
                        'result' => $result['result'] ?? null,
                        'reused' => true
                    ];

                    $pages[] = [
                        'page_number' => $pageNumber,
                        'page_id' => $result['page_id'],
                        'variant_id' => $result['variant_id'] ?? null,
                        'image_url' => $result['result_image_url'],
                        'result_image_url' => $result['result_image_url'],
                        'result' => $result['result'] ?? null,
                        'reused' => true
                    ];
                }

                // 更新订单项
                $orderItem->face_swap_batch_id = $batchId;
                $orderItem->result_images = $resultImages;
                $orderItem->setStatus(\App\Models\OrderItem::STATUS_COMPLETED);
                $orderItem->processing_progress = 100;
                $orderItem->save();

                // 更新关联的预览记录
                if ($orderItem->preview) {
                    $preview = $orderItem->preview;
                    $previewData = $preview->preview_data ?? [];
                    $previewData['pages'] = $pages;
                    $preview->preview_data = $previewData;
                    $preview->result_images = $resultImages;
                    $preview->save();
                }

                Log::info('订单项复用结果更新成功', [
                    'order_item_id' => $orderItem->id,
                    'batch_id' => $batchId,
                    'reused_pages_count' => count($pages)
                ]);
            }

        } catch (\Exception $e) {
            Log::error('更新订单项复用结果失败', [
                'order_id' => $orderId,
                'batch_id' => $batchId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 获取批次状态
     */
    public function getBatchStatus(string $batchId)
    {
        try {
            // 获取批次记录
            $batchRecord = AiFaceTask::where('batch_id', $batchId)
                ->where('type', 'batch')
                ->first();

            if (!$batchRecord) {
                throw new \Exception('批次不存在');
            }

            // 获取批次中的所有任务
            $tasks = AiFaceTask::where('batch_id', $batchId)
                ->where('type', 'task')
                ->orderBy('task_index')
                ->get();

            $totalTasks = $tasks->count();
            $completedTasks = $tasks->where('status', 'completed')->count();
            $failedTasks = $tasks->where('status', 'failed')->count();
            $processingTasks = $tasks->where('status', 'processing')->count();
            $pendingTasks = $tasks->where('status', 'pending')->count();

            // 计算进度
            $progress = $totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100, 2) : 0;

            // 确定批次状态
            $batchStatus = 'pending';
            if ($completedTasks == $totalTasks) {
                $batchStatus = 'completed';
            } elseif ($failedTasks > 0 && ($completedTasks + $failedTasks) == $totalTasks) {
                $batchStatus = 'failed';
            } elseif ($processingTasks > 0 || $completedTasks > 0) {
                $batchStatus = 'processing';
            }

            // 获取已完成任务的结果
            $completedResults = $tasks->where('status', 'completed')->map(function ($task) {
                return [
                    'task_id' => $task->id,
                    'page_id' => $task->page_id,
                    'variant_id' => $task->variant_id,
                    'result_image_url' => $task->result_image_url,
                    'result' => $task->result
                ];
            })->values();

            return [
                'success' => true,
                'batch_id' => $batchId,
                'status' => $batchStatus,
                'progress' => $progress,
                'total_tasks' => $totalTasks,
                'completed_tasks' => $completedTasks,
                'failed_tasks' => $failedTasks,
                'processing_tasks' => $processingTasks,
                'pending_tasks' => $pendingTasks,
                'completed_results' => $completedResults,
                'is_priority' => $batchRecord->is_priority,
                'created_at' => $batchRecord->created_at,
                'updated_at' => $batchRecord->updated_at
            ];
        } catch (\Exception $e) {
            Log::error('获取批次状态失败', [
                'batch_id' => $batchId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    /**
     * 调用甲方换脸API
     */
    private function callFaceSwapApi(string $imageUrl, array $faceImageUrl, int $id)
    {
        try {
            Log::info('开始调用换脸API', [
                'image_url' => $imageUrl,
                'face_image_url' => $faceImageUrl
            ]);

            // 验证图片URL
            if (empty($imageUrl) || empty($faceImageUrl)) {
                throw new \Exception('图片URL不能为空');
            }

            // 获取API密钥
            $keyModel = \App\Models\AiApiKey::getAvailableKey();
            $apiKey = $keyModel->api_key;

            // 增加当前任务计数
            $keyModel->incrementTasks();

            // 获取任务记录，检查是否有蒙版图片
            $task = AiFaceTask::find($id);
            $maskImageUrl = $task ? $task->mask_image : null;

            try {
                // 增加内存限制
                $currentMemoryLimit = ini_get('memory_limit');
                ini_set('memory_limit', '1024M');

                // 下载目标图片
                $inputImagePath = $this->downloadImage($imageUrl);

                // 检查目标图片大小，如果过大则记录警告
                $inputFileSize = filesize($inputImagePath);
                $inputFileSizeMB = round($inputFileSize / 1024 / 1024, 2);
                if ($inputFileSizeMB > 10) {
                    Log::warning('目标图片文件较大', [
                        'size_mb' => $inputFileSizeMB
                    ]);
                }

                // 下载人脸图片，每次只处理一个，避免同时加载多个大文件到内存
                $faceImagePath = [];
                foreach ($faceImageUrl as $item) {
                    // 强制垃圾回收
                    gc_collect_cycles();

                    $temp = $this->downloadImage($item);
                    if (!file_exists($temp) || !filesize($temp)) {
                        throw new \Exception('人脸图片文件无效');
                    }

                    // 检查人脸图片大小
                    $faceFileSize = filesize($temp);
                    $faceFileSizeMB = round($faceFileSize / 1024 / 1024, 2);
                    if ($faceFileSizeMB > 5) {
                        Log::warning('人脸图片文件较大', [
                            'size_mb' => $faceFileSizeMB
                        ]);
                    }

                    $faceImagePath[] = $temp;
                }

                // 下载蒙版图片（如果有）
                $maskImagePath = null;
                if (!empty($maskImageUrl)) {
                    // 强制垃圾回收
                    gc_collect_cycles();

                    $maskImagePath = $this->downloadImage($maskImageUrl);
                    Log::info('成功下载蒙版图片', ['mask_image_url' => $maskImageUrl]);

                    // 检查蒙版图片大小
                    $maskFileSize = filesize($maskImagePath);
                    $maskFileSizeMB = round($maskFileSize / 1024 / 1024, 2);
                    if ($maskFileSizeMB > 5) {
                        Log::warning('蒙版图片文件较大', [
                            'size_mb' => $maskFileSizeMB
                        ]);
                    }
                }

                Log::info('解析后的文件路径', [
                    'input_image_path' => $inputImagePath,
                    'face_image_path' => $faceImagePath,
                    'mask_image_path' => $maskImagePath
                ]);

                // 检查文件是否存在且有效
                if (!file_exists($inputImagePath) || !filesize($inputImagePath)) {
                    throw new \Exception('目标图片文件无效');
                }

                if ($maskImagePath && (!file_exists($maskImagePath) || !filesize($maskImagePath))) {
                    Log::warning('蒙版图片文件无效', ['mask_image_url' => $maskImageUrl]);
                    throw new \Exception('蒙版图片文件无效');
                }
            } catch (\Exception $e) {
                Log::error('下载图片失败', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                // 恢复原始内存限制
                ini_set('memory_limit', $currentMemoryLimit ?? '512M');

                throw new \Exception('下载图片失败: ' . $e->getMessage());
            }

            // 上传目标图片到甲方服务器
            try {
                // 检查文件大小，如果过大则记录警告
                $inputFileSize = filesize($inputImagePath);
                $inputFileSizeMB = round($inputFileSize / 1024 / 1024, 2);

                if ($inputFileSizeMB > 10) {
                    // 对于大文件，使用流式上传
                    $inputResource = fopen($inputImagePath, 'r');
                    $inputResponse = Http::timeout(60)->attach(
                        'file',
                        $inputResource,
                        basename($inputImagePath)
                    )->post('https://www.runninghub.cn/task/openapi/upload', [
                        'apiKey' => $apiKey,
                        'fileType' => 'image'
                    ]);

                    if (is_resource($inputResource)) {
                        fclose($inputResource);
                    }
                } else {
                    // 对于小文件，使用常规上传
                    $inputResponse = Http::timeout(60)->attach(
                        'file',
                        file_get_contents($inputImagePath),
                        basename($inputImagePath)
                    )->post('https://www.runninghub.cn/task/openapi/upload', [
                        'apiKey' => $apiKey,
                        'fileType' => 'image'
                    ]);
                }

                if (!$inputResponse->successful()) {
                    throw new \Exception('上传目标图片失败：' . $inputResponse->json('msg'));
                }

                // 强制垃圾回收
                gc_collect_cycles();
            } catch (\Exception $e) {
                Log::error('上传目标图片失败', [
                    'error' => $e->getMessage()
                ]);
                throw $e;
            }

            // 构建请求数据
            $inputFileName = $inputResponse->json('data.fileName');
            $nodeInfoList = [
                [
                    'nodeId' => "238",
                    'fieldName' => "image",
                    'fieldValue' => $inputFileName
                ]
            ];

            // 上传人脸图片到甲方服务器
            foreach ($faceImagePath as $k => $value) {
                try {
                    // 强制垃圾回收
                    gc_collect_cycles();

                    // 检查文件大小
                    $faceFileSize = filesize($value);
                    $faceFileSizeMB = round($faceFileSize / 1024 / 1024, 2);

                    if ($faceFileSizeMB > 5) {
                        // 对于大文件，使用流式上传
                        $faceResource = fopen($value, 'r');
                        $faceResponse = Http::timeout(60)->attach(
                            'file',
                            $faceResource,
                            basename($value)
                        )->post('https://www.runninghub.cn/task/openapi/upload', [
                            'apiKey' => $apiKey,
                            'fileType' => 'image'
                        ]);

                        if (is_resource($faceResource)) {
                            fclose($faceResource);
                        }
                    } else {
                        // 对于小文件，使用常规上传
                        $faceResponse = Http::timeout(60)->attach(
                            'file',
                            file_get_contents($value),
                            basename($value)
                        )->post('https://www.runninghub.cn/task/openapi/upload', [
                            'apiKey' => $apiKey,
                            'fileType' => 'image'
                        ]);
                    }

                    if (!$faceResponse->successful()) {
                        throw new \Exception('上传人脸图片失败：' . $faceResponse->json('msg'));
                    }

                    $faceFileName = $faceResponse->json('data.fileName');
                    if ($k == 0) {
                        $nodeId = "240";
                    } elseif ($k == 1) {
                        $nodeId = "246";
                    } else {
                        $nodeId = "247";
                    }
                    $nodeInfoList[] = [
                        'nodeId' => $nodeId,
                        'fieldName' => "image",
                        'fieldValue' => $faceFileName
                    ];
                } catch (\Exception $e) {
                    Log::error('上传人脸图片失败', [
                        'index' => $k,
                        'error' => $e->getMessage()
                    ]);
                    throw $e;
                }
            }

            // 上传蒙版图片（如果有）
            $maskFileName = null;
            if ($maskImagePath) {
                try {
                    // 强制垃圾回收
                    gc_collect_cycles();

                    // 检查文件大小
                    $maskFileSize = filesize($maskImagePath);
                    $maskFileSizeMB = round($maskFileSize / 1024 / 1024, 2);

                    if ($maskFileSizeMB > 5) {
                        // 对于大文件，使用流式上传
                        $maskResource = fopen($maskImagePath, 'r');
                        $maskResponse = Http::timeout(60)->attach(
                            'file',
                            $maskResource,
                            basename($maskImagePath)
                        )->post('https://www.runninghub.cn/task/openapi/upload', [
                            'apiKey' => $apiKey,
                            'fileType' => 'image'
                        ]);

                        if (is_resource($maskResource)) {
                            fclose($maskResource);
                        }
                    } else {
                        // 对于小文件，使用常规上传
                        $maskResponse = Http::timeout(60)->attach(
                            'file',
                            file_get_contents($maskImagePath),
                            basename($maskImagePath)
                        )->post('https://www.runninghub.cn/task/openapi/upload', [
                            'apiKey' => $apiKey,
                            'fileType' => 'image'
                        ]);
                    }

                    if (!$maskResponse->successful()) {
                        Log::warning('上传蒙版图片失败，将使用默认蒙版', [
                            'error' => $maskResponse->json('msg')
                        ]);
                    } else {
                        $maskFileName = $maskResponse->json('data.fileName');
                        Log::info('成功上传蒙版图片', ['mask_file_name' => $maskFileName]);
                    }
                } catch (\Exception $e) {
                    Log::error('上传蒙版图片失败', [
                        'error' => $e->getMessage()
                    ]);
                    // 蒙版图片上传失败不是致命错误，继续处理
                }
            }

            // 如果有蒙版，使用蒙版，否则使用输入图像
            if ($maskFileName) {
                $nodeInfoList[] = [
                    'nodeId' => "239",
                    'fieldName' => "image",
                    'fieldValue' => $maskFileName
                ];
            } else {
                $nodeInfoList[] = [
                    'nodeId' => "239",
                    'fieldName' => "image",
                    'fieldValue' => $inputFileName // 如果没有蒙版，使用输入图像
                ];
            }

            $requestData = [
                'workflowId' => config('services.ai_face.workflow_id'),
                'apiKey' => $apiKey,
                'nodeInfoList' => $nodeInfoList
            ];

            // 发送创建任务请求
            $response = Http::timeout(60)->post('https://www.runninghub.cn/task/openapi/create', $requestData);

            if (!$response->successful()) {
                throw new \Exception('创建换脸任务失败：' . $response->json('msg'));
            }
            //临时记录
            Log::info('临时记录响应数据', ['response_json' => $response->json()]);

            // 获取任务ID
            $taskId = $response->json('data.taskId');

            //如果$taskId == null,直接返回失败
            if (empty($taskId)) {
                throw new \Exception('创建换脸任务失败：taskId为空');
            }
            //更新ai_face_tasks表的task_id
            AiFaceTask::where('id', $id)->update(['task_id' => $taskId]);

            // 轮询任务结果
            $maxAttempts = 45; // 最大尝试次数
            $attempt = 0;
            $resultImageUrl = null;

            Log::info('开始轮询任务结果', [
                'task_id' => $taskId,
                'max_attempts' => $maxAttempts
            ]);

            while ($attempt < $maxAttempts) {
                $attempt++;

                // 查询任务状态
                try {
                    $apiUrl = config('services.ai_face.api_url', 'https://www.runninghub.cn') . '/task/openapi/outputs';

                    $statusResponse = Http::withHeaders([
                        'Authorization' => 'Bearer ' . $apiKey,
                        'Content-Type' => 'application/json'
                    ])->timeout(60)->post($apiUrl, [
                        'taskId' => $taskId,
                        'apiKey' => $apiKey,
                    ]);

                    if (!$statusResponse->successful()) {
                        Log::warning('查询任务状态请求失败', [
                            'task_id' => $taskId,
                            'attempt' => $attempt,
                            'status_code' => $statusResponse->status()
                        ]);

                        if ($attempt >= $maxAttempts) {
                            throw new \Exception('查询任务状态失败：' . $statusResponse->json('msg'));
                        }

                        sleep(10);
                        continue;
                    }

                    $mainCode = $statusResponse->json('code');

                    // 如果任务完成
                    if ($mainCode === 0) {
                        $resultData = $statusResponse->json('data');

                        // 处理不同格式的结果
                        if (is_array($resultData)) {
                            // 检查是否直接返回数组
                            if (!empty($resultData) && isset($resultData[0]['fileUrl'])) {
                                $resultImageUrl = $resultData[0]['fileUrl'];
                                break;
                            }
                            // 检查data.resultData格式
                            else if (
                                isset($resultData['resultData']) && is_array($resultData['resultData']) &&
                                !empty($resultData['resultData']) && isset($resultData['resultData'][0]['fileUrl'])
                            ) {
                                $resultImageUrl = $resultData['resultData'][0]['fileUrl'];
                                break;
                            }
                        }

                        // 直接从响应中提取任何可能的URL
                        $responseString = json_encode($statusResponse->json());
                        if (preg_match('/https?:\/\/[^\s"\']+\.(png|jpg|jpeg|gif)/i', $responseString, $matches)) {
                            $resultImageUrl = $matches[0];
                            break;
                        }
                    }
                    // 如果任务正在运行
                    else if ($mainCode === 804) {
                        // 继续等待
                    }
                    // 其他错误状态
                    else if ($mainCode !== 0 && $mainCode !== 804) {
                        $errorMsg = $statusResponse->json('msg') ?? '未知错误';
                        Log::error('换脸任务失败', [
                            'task_id' => $taskId,
                            'main_code' => $mainCode,
                            'msg' => $errorMsg,
                            'attempt' => $attempt
                        ]);

                        // 对于明确的错误状态，直接抛出异常，不再继续轮询
                        throw new \Exception("换脸任务失败 (错误码: {$mainCode}): {$errorMsg}");
                    }
                } catch (\Exception $e) {
                    Log::warning('轮询过程中发生错误', [
                        'task_id' => $taskId,
                        'attempt' => $attempt,
                        'error' => $e->getMessage()
                    ]);

                    if ($attempt >= $maxAttempts) {
                        throw new \Exception('轮询任务结果失败: ' . $e->getMessage());
                    }
                }

                // 等待一段时间后再次查询
                sleep(10);
            }

            // 清理临时文件
            $this->cleanupTempFiles($inputImagePath, $faceImagePath, $maskImagePath);

            // 强制垃圾回收
            gc_collect_cycles();

            // 恢复原始内存限制
            if (isset($currentMemoryLimit)) {
                ini_set('memory_limit', $currentMemoryLimit);
            }

            if ($resultImageUrl) {
                Log::info('换脸任务处理成功', [
                    'task_id' => $taskId,
                    'result_url' => $resultImageUrl
                ]);
                return [
                    'success' => true,
                    'result_image_url' => $resultImageUrl
                ];
            } else {
                Log::error('换脸任务超时或未返回结果图片URL', [
                    'task_id' => $taskId,
                    'attempts' => $attempt
                ]);
                throw new \Exception('换脸任务超时或未返回结果图片URL');
            }
        } catch (\Exception $e) {
            // 清理临时文件
            $this->cleanupTempFiles($inputImagePath ?? null, $faceImagePath ?? [], $maskImagePath ?? null);

            // 强制垃圾回收
            gc_collect_cycles();

            // 恢复原始内存限制
            if (isset($currentMemoryLimit)) {
                ini_set('memory_limit', $currentMemoryLimit);
            }

            Log::error('调用换脸API失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => '调用换脸API失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 清理临时文件
     */
    private function cleanupTempFiles(?string $inputImagePath, array $faceImagePath, ?string $maskImagePath)
    {
        // 清理输入图片
        if ($inputImagePath && file_exists($inputImagePath)) {
            @unlink($inputImagePath);
        }

        // 清理人脸图片
        foreach ($faceImagePath as $path) {
            if ($path && file_exists($path)) {
                @unlink($path);
            }
        }

        // 清理蒙版图片
        if ($maskImagePath && file_exists($maskImagePath)) {
            @unlink($maskImagePath);
        }
    }

    /**
     * 下载图片到临时文件
     */
    private function downloadImage(string $url)
    {
        // URL验证
        if (empty($url)) {
            throw new \Exception('图片URL不能为空');
        }

        // 创建临时文件
        $tempFile = tempnam(sys_get_temp_dir(), 'img_');

        // 处理URL格式
        if (!preg_match('/^https?:\/\//i', $url)) {
            if (strpos($url, '//') === 0) {
                $url = 'https:' . $url;
            } else if (strpos($url, '/') === 0) {
                $url = config('app.url') . $url;
            }
        }

        try {
            // 增加内存限制
            $currentMemoryLimit = ini_get('memory_limit');
            ini_set('memory_limit', '1024M');

            // 下载图片
            $response = Http::timeout(60)->get($url);

            if (!$response->successful()) {
                throw new \Exception('下载图片失败，HTTP状态码: ' . $response->status());
            }

            $imageData = $response->body();

            // 检查数据大小，如果过大则分块写入
            $dataSize = strlen($imageData);
            $dataSizeMB = round($dataSize / 1024 / 1024, 2);

            if ($dataSizeMB > 5) {
                // 对于大文件，使用分块写入以减少峰值内存使用
                $fileStream = fopen($tempFile, 'w');
                $chunkSize = 8192; // 8KB

                for ($i = 0; $i < $dataSize; $i += $chunkSize) {
                    $chunk = substr($imageData, $i, $chunkSize);
                    fwrite($fileStream, $chunk);

                    // 每处理几个块后强制垃圾回收
                    if (($i / $chunkSize) % 10 === 0) {
                        gc_collect_cycles();
                    }
                }

                fclose($fileStream);
            } else {
                // 对于小文件，直接写入
                file_put_contents($tempFile, $imageData);
            }

            // 验证文件大小
            if (filesize($tempFile) === 0) {
                throw new \Exception('下载的图片文件大小为0');
            }

            // 检查文件大小，如果过大则记录警告
            $fileSize = filesize($tempFile);
            $fileSizeMB = round($fileSize / 1024 / 1024, 2);
            if ($fileSizeMB > 10) {
                Log::warning('下载的图片文件较大', [
                    'url' => $url,
                    'size_mb' => $fileSizeMB
                ]);
            }

            // 恢复原始内存限制
            ini_set('memory_limit', $currentMemoryLimit);

            return $tempFile;
        } catch (\Exception $e) {
            // 清理临时文件
            @unlink($tempFile);

            // 恢复原始内存限制
            ini_set('memory_limit', $currentMemoryLimit ?? '512M');

            Log::error('下载图片失败', [
                'url' => $url,
                'error' => $e->getMessage()
            ]);

            throw new \Exception('下载图片失败: ' . $e->getMessage());
        }
    }

    /**
     * 生成不同分辨率的图片
     */
    private function generateResolutions(string $imageUrl)
    {
        try {
            // 增加内存限制以处理大图片，但设置一个合理的上限
            $currentMemoryLimit = ini_get('memory_limit');
            $newMemoryLimit = '1024M'; // 增加到1GB
            ini_set('memory_limit', $newMemoryLimit);

            Log::info('内存限制已调整', [
                'original_limit' => $currentMemoryLimit,
                'new_limit' => $newMemoryLimit
            ]);

            // 下载原始图片
            $tempFile = $this->downloadImage($imageUrl);

            // 获取图片信息以决定处理策略
            $imageInfo = @getimagesize($tempFile);
            if (!$imageInfo) {
                throw new \Exception('无法获取图片信息');
            }

            $origWidth = $imageInfo[0];
            $origHeight = $imageInfo[1];
            $megapixels = $origWidth * $origHeight / 1000000;

            Log::info('图片信息', [
                'dimensions' => "{$origWidth}x{$origHeight}",
                'megapixels' => round($megapixels, 2)
            ]);

            // 如果图片非常大（超过1000万像素），使用分块处理策略
            if ($megapixels > 10) {
                Log::info('检测到大图片，使用优化处理策略');
                return $this->generateResolutionsForLargeImage($tempFile, $origWidth, $origHeight, $imageUrl);
            }

            // 生成结果数组
            $result = [
                'standard' => '',  // 原始分辨率
                'high_res' => '',  // 300dpi
                'low_res' => ''    // 70dpi
            ];

            // 保存原始分辨率图片到 S3
            $standardPath = 'processed/' . uniqid() . '_standard.jpg';
            Storage::disk('s3_faceswap')->put($standardPath, file_get_contents($tempFile));
            $result['standard'] = Storage::disk('s3_faceswap')->url($standardPath);

            // 清理原始图片对象以释放内存
            unset($imageInfo);
            gc_collect_cycles();

            // 生成300dpi高分辨率图片
            $highResPath = 'processed/' . uniqid() . '_300dpi.jpg';
            $scaleFactor = 300 / 72; // 4.1667倍
            $highResWidth = (int)($origWidth * $scaleFactor);
            $highResHeight = (int)($origHeight * $scaleFactor);

            // 使用Imagick处理高分辨率，如果失败则使用Intervention Image
            try {
                $highResTempFile = $this->generateHighResImage($tempFile, $highResWidth, $highResHeight);

                // 上传到 S3 存储
                Storage::disk('s3_faceswap')->put($highResPath, file_get_contents($highResTempFile));
                $result['high_res'] = Storage::disk('s3_faceswap')->url($highResPath);

                // 清理临时文件
                @unlink($highResTempFile);
                unset($highResTempFile);
                gc_collect_cycles();
            } catch (\Exception $e) {
                Log::warning('生成高分辨率图片失败', ['error' => $e->getMessage()]);
                $result['high_res'] = $result['standard']; // 使用原始图片作为回退
            }

            // 生成72dpi低分辨率图片（缩小版本）
            $lowResPath = 'processed/' . uniqid() . '_72dpi.jpg';
            $lowResWidth = (int)($origWidth * 0.5);
            $lowResHeight = (int)($origHeight * 0.5);

            try {
                $lowResTempFile = $this->generateLowResImage($tempFile, $lowResWidth, $lowResHeight);

                // 上传到 S3 存储
                Storage::disk('s3_faceswap')->put($lowResPath, file_get_contents($lowResTempFile));
                $result['low_res'] = Storage::disk('s3_faceswap')->url($lowResPath);

                // 清理临时文件
                @unlink($lowResTempFile);
                unset($lowResTempFile);
                gc_collect_cycles();
            } catch (\Exception $e) {
                Log::warning('生成低分辨率图片失败', ['error' => $e->getMessage()]);
                $result['low_res'] = $result['standard']; // 使用原始图片作为回退
            }

            // 清理临时文件
            @unlink($tempFile);
            unset($tempFile);
            gc_collect_cycles();

            // 恢复原始内存限制
            ini_set('memory_limit', $currentMemoryLimit);

            return $result;
        } catch (\Exception $e) {
            Log::error('生成不同分辨率图片失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 恢复原始内存限制
            ini_set('memory_limit', $currentMemoryLimit ?? '512M');

            // 失败时返回原始图片URL
            return [
                'standard' => $imageUrl,
                'high_res' => $imageUrl,
                'low_res' => $imageUrl
            ];
        }
    }

    /**
     * 为大图片生成分辨率版本
     */
    private function generateResolutionsForLargeImage(string $tempFile, int $origWidth, int $origHeight, string $originalUrl)
    {
        // 对于大图片，我们只生成标准分辨率和低分辨率版本，跳过高分辨率以避免内存问题
        $result = [
            'standard' => '',  // 原始分辨率
            'high_res' => '',  // 300dpi
            'low_res' => ''    // 70dpi
        ];

        // 保存原始分辨率图片到 S3
        $standardPath = 'processed/' . uniqid() . '_standard.jpg';
        Storage::disk('s3_faceswap')->put($standardPath, file_get_contents($tempFile));
        $result['standard'] = Storage::disk('s3_faceswap')->url($standardPath);
        $result['high_res'] = $result['standard']; // 大图片的高分辨率版本使用原始图片

        // 生成低分辨率版本
        $lowResPath = 'processed/' . uniqid() . '_72dpi.jpg';
        $lowResWidth = (int)($origWidth * 0.5);
        $lowResHeight = (int)($origHeight * 0.5);

        try {
            $lowResTempFile = $this->generateLowResImage($tempFile, $lowResWidth, $lowResHeight);

            // 上传到 S3 存储
            Storage::disk('s3_faceswap')->put($lowResPath, file_get_contents($lowResTempFile));
            $result['low_res'] = Storage::disk('s3_faceswap')->url($lowResPath);

            // 清理临时文件
            @unlink($lowResTempFile);
            unset($lowResTempFile);
            gc_collect_cycles();
        } catch (\Exception $e) {
            Log::warning('为大图片生成低分辨率版本失败', ['error' => $e->getMessage()]);
            $result['low_res'] = $result['standard']; // 使用原始图片作为回退
        }

        return $result;
    }

    /**
     * 生成高分辨率图片
     */
    private function generateHighResImage(string $sourceFile, int $width, int $height)
    {
        $tempFile = tempnam(sys_get_temp_dir(), 'high_');

        // 优先使用Imagick，因为它对内存管理更好
        if (extension_loaded('imagick') && class_exists('\\Imagick')) {
            try {
                // 使用Imagick处理
                $imagick = new \Imagick($sourceFile);

                // 设置原始DPI为72
                $imagick->setImageResolution(72, 72);
                $imagick->setImageUnits(\Imagick::RESOLUTION_PIXELSPERINCH);

                // 调整图像大小
                $imagick->resizeImage($width, $height, \Imagick::FILTER_LANCZOS, 1);

                // 设置新的DPI为300
                $imagick->setImageResolution(300, 300);
                $imagick->setImageFormat('jpeg');
                $imagick->setImageCompressionQuality(90); // 降低质量以减少内存使用

                // 优化内存使用
                $imagick->stripImage(); // 移除元数据

                // 写入文件
                $imagick->writeImage($tempFile);

                // 清理资源
                $imagick->clear();
                $imagick->destroy();

                return $tempFile;
            } catch (\Exception $e) {
                Log::warning('Imagick处理高分辨率图片失败，回退到Intervention Image', ['error' => $e->getMessage()]);
                @unlink($tempFile); // 清理可能创建的临时文件
            }
        }

        // 如果Imagick不可用或失败，使用Intervention Image
        $image = $this->imageManager->read($sourceFile);
        $image->resize($width, $height)
            ->save($tempFile, quality: 90);

        // 清理资源
        unset($image);
        gc_collect_cycles();

        return $tempFile;
    }

    /**
     * 生成低分辨率图片
     */
    private function generateLowResImage(string $sourceFile, int $width, int $height)
    {
        $tempFile = tempnam(sys_get_temp_dir(), 'low_');

        // 优先使用Imagick
        if (extension_loaded('imagick') && class_exists('\\Imagick')) {
            try {
                $imagick = new \Imagick($sourceFile);

                // 调整图像大小
                $imagick->resizeImage($width, $height, \Imagick::FILTER_LANCZOS, 1);

                // 设置DPI为72
                $imagick->setImageResolution(72, 72);
                $imagick->setImageUnits(\Imagick::RESOLUTION_PIXELSPERINCH);
                $imagick->setImageFormat('jpeg');
                $imagick->setImageCompressionQuality(80);

                // 优化内存使用
                $imagick->stripImage(); // 移除元数据

                // 写入文件
                $imagick->writeImage($tempFile);

                // 清理资源
                $imagick->clear();
                $imagick->destroy();

                return $tempFile;
            } catch (\Exception $e) {
                Log::warning('Imagick处理低分辨率图片失败，回退到Intervention Image', ['error' => $e->getMessage()]);
                @unlink($tempFile); // 清理可能创建的临时文件
            }
        }

        // 如果Imagick不可用或失败，使用Intervention Image
        $image = $this->imageManager->read($sourceFile);
        $image->resize($width, $height)
            ->save($tempFile, quality: 80);

        // 清理资源
        unset($image);
        gc_collect_cycles();

        return $tempFile;
    }

    /**
     * 更新批次进度
     */
    private function updateBatchProgress(string $batchId)
    {
        try {
            // 获取批次中的所有任务
            $tasks = AiFaceTask::where('batch_id', $batchId)
                ->where('type', 'task')
                ->get();

            $totalTasks = $tasks->count();
            $completedTasks = $tasks->whereIn('status', ['completed', 'failed'])->count();
            $progress = $totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100, 2) : 0;

            // 更新批次记录
            $batchRecord = AiFaceTask::where('batch_id', $batchId)
                ->where('type', 'batch')
                ->first();

            if ($batchRecord) {
                $batchRecord->completed_tasks = $completedTasks;
                $batchRecord->progress = $progress;

                // 如果所有任务都完成了，更新批次状态
                if ($completedTasks >= $totalTasks) {
                    $failedTasks = $tasks->where('status', 'failed')->count();
                    $batchRecord->status = $failedTasks > 0 ? 'completed_with_errors' : 'completed';

                    // 发送批次完成通知
                    $this->sendBatchCompletedNotification($batchRecord);
                }

                $batchRecord->save();
            }

            Log::info('更新批次进度', [
                'batch_id' => $batchId,
                'progress' => $progress,
                'completed_tasks' => $completedTasks,
                'total_tasks' => $totalTasks
            ]);
        } catch (\Exception $e) {
            Log::error('更新批次进度失败', [
                'batch_id' => $batchId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 更新 PicbookPreview 表
     */
    private function updatePicbookPreview(string $batchId, string $status, ?string $errorMessage = null)
    {
        try {
            $preview = PicbookPreview::where('batch_id', $batchId)->first();

            if ($preview) {
                // 获取批次中已完成的任务结果
                $completedTasks = AiFaceTask::where('batch_id', $batchId)
                    ->where('type', 'task')
                    ->where('status', 'completed')
                    ->get();

                $resultImages = [];
                $pageResults = [];

                foreach ($completedTasks as $task) {
                    if ($task->result_image_url) {
                        $resultImages[] = [
                            'page_id' => $task->page_id,
                            'variant_id' => $task->variant_id,
                            'result_image_url' => $task->result_image_url,
                            'result' => $task->result
                        ];

                        // 建立页面ID到结果的映射
                        $pageResults[$task->page_id] = [
                            'result_image_url' => $task->result_image_url,
                            'result' => $task->result
                        ];
                    }
                }

                // 更新预览表数据
                $this->updatePreviewData($preview, $resultImages, $pageResults, $status, $errorMessage);
            }

            // 检查是否是全书预览（订单相关的批次）
            $this->updateOrderItemIfNeeded($batchId, $status, $errorMessage);

        } catch (\Exception $e) {
            Log::error('更新预览数据失败', [
                'batch_id' => $batchId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 更新预览数据的核心逻辑
     */
    private function updatePreviewData($preview, $resultImages, $pageResults, $status, $errorMessage = null)
    {
        try {
            // 更新结果图片
            $preview->result_images = $resultImages;

            // 更新预览状态
            if ($status === 'completed') {
                $preview->status = PicbookPreview::STATUS_COMPLETED;
            } elseif ($status === 'failed') {
                $preview->status = PicbookPreview::STATUS_FAILED;
            }

            // 如果有错误信息，记录到notes字段
            if ($errorMessage) {
                $preview->notes = ($preview->notes ? $preview->notes . "\n" : '') .
                    "Error: " . $errorMessage;
            }

            // 更新页面数据（如果预览数据中有页面信息）
            $previewData = $preview->preview_data ?? [];
            if (isset($previewData['pages']) && is_array($previewData['pages'])) {
                foreach ($previewData['pages'] as &$page) {
                    $pageId = $page['page_id'] ?? null;
                    if ($pageId && isset($pageResults[$pageId])) {
                        $page['result_image_url'] = $pageResults[$pageId]['result_image_url'];
                        $page['result'] = $pageResults[$pageId]['result'];
                    }
                }
                $preview->preview_data = $previewData;
            }

            $preview->save();

            Log::info('预览数据更新成功', [
                'preview_id' => $preview->id,
                'batch_id' => $preview->batch_id,
                'status' => $status,
                'result_count' => count($resultImages)
            ]);

        } catch (\Exception $e) {
            Log::error('更新预览数据失败', [
                'preview_id' => $preview->id ?? null,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 如果是订单相关的批次，更新订单项数据（支持合并已有结果）
     */
    private function updateOrderItemIfNeeded($batchId, $status, $errorMessage = null)
    {
        try {
            // 查找是否有订单项关联这个批次
            $orderItem = \App\Models\OrderItem::where('face_swap_batch_id', $batchId)->first();

            if ($orderItem) {
                // 获取批次配置，检查是否有已存在的结果需要合并
                $batchRecord = AiFaceTask::where('batch_id', $batchId)
                    ->where('type', 'batch')
                    ->first();

                $existingResults = [];
                if ($batchRecord && isset($batchRecord->config['existing_results'])) {
                    $existingResults = $batchRecord->config['existing_results'];
                    Log::info('发现需要合并的已有结果', [
                        'batch_id' => $batchId,
                        'existing_count' => count($existingResults)
                    ]);
                }

                // 获取批次中已完成的任务结果（新处理的）
                $completedTasks = AiFaceTask::where('batch_id', $batchId)
                    ->where('type', 'task')
                    ->where('status', 'completed')
                    ->orderBy('task_index')
                    ->get();

                $resultImages = [];
                $pages = [];

                // 先添加已存在的结果
                foreach ($existingResults as $existingResult) {
                    $pageNumber = $this->getPageNumber($existingResult['page_id']);
                    $resultImages[] = [
                        'page_id' => $existingResult['page_id'],
                        'variant_id' => $existingResult['variant_id'] ?? null,
                        'page_number' => $pageNumber,
                        'result_image_url' => $existingResult['result_image_url'],
                        'result' => $existingResult['result'] ?? null,
                        'reused' => true
                    ];

                    $pages[] = [
                        'page_number' => $pageNumber,
                        'page_id' => $existingResult['page_id'],
                        'variant_id' => $existingResult['variant_id'] ?? null,
                        'image_url' => $existingResult['result_image_url'],
                        'result_image_url' => $existingResult['result_image_url'],
                        'result' => $existingResult['result'] ?? null,
                        'reused' => true
                    ];
                }

                // 再添加新处理的结果
                foreach ($completedTasks as $task) {
                    if ($task->result_image_url) {
                        $resultImages[] = [
                            'page_id' => $task->page_id,
                            'variant_id' => $task->variant_id,
                            'page_number' => $this->getPageNumber($task->page_id),
                            'result_image_url' => $task->result_image_url,
                            'result' => $task->result,
                            'task_index' => $task->task_index,
                            'reused' => false
                        ];

                        // 构建页面数据
                        $pages[] = [
                            'page_number' => $this->getPageNumber($task->page_id),
                            'page_id' => $task->page_id,
                            'variant_id' => $task->variant_id,
                            'image_url' => $task->result_image_url,
                            'original_image_url' => $task->image_url,
                            'result' => $task->result,
                            'reused' => false
                        ];
                    }
                }

                // 按页面号排序
                usort($resultImages, function($a, $b) {
                    return $a['page_number'] <=> $b['page_number'];
                });
                usort($pages, function($a, $b) {
                    return $a['page_number'] <=> $b['page_number'];
                });

                // 更新订单项的结果数据
                $orderItem->result_images = $resultImages;

                // 更新订单项状态
                if ($status === 'completed') {
                    $orderItem->setStatus(\App\Models\OrderItem::STATUS_COMPLETED);
                    $orderItem->processing_progress = 100;
                } elseif ($status === 'failed') {
                    $orderItem->setStatus('failed');
                    if ($errorMessage) {
                        $orderItem->notes = ($orderItem->notes ? $orderItem->notes . "\n" : '') .
                            "Face swap failed: " . $errorMessage;
                    }
                }

                $orderItem->save();

                // 同时更新关联的预览记录的页面数据
                if ($orderItem->preview && !empty($pages)) {
                    $preview = $orderItem->preview;
                    $previewData = $preview->preview_data ?? [];
                    $previewData['pages'] = $pages;
                    $preview->preview_data = $previewData;
                    $preview->result_images = $resultImages;
                    $preview->save();
                }

                Log::info('订单项数据更新成功（含复用结果）', [
                    'order_item_id' => $orderItem->id,
                    'batch_id' => $batchId,
                    'status' => $status,
                    'total_results' => count($resultImages),
                    'new_results' => $completedTasks->count(),
                    'reused_results' => count($existingResults),
                    'pages_count' => count($pages)
                ]);
            }

        } catch (\Exception $e) {
            Log::error('更新订单项数据失败', [
                'batch_id' => $batchId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 根据页面ID获取页面号
     */
    private function getPageNumber($pageId)
    {
        try {
            $page = \App\Models\PicbookPage::find($pageId);
            return $page ? $page->page_number : 0;
        } catch (\Exception $e) {
            Log::warning('获取页面号失败', [
                'page_id' => $pageId,
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }

    /**
     * 发送任务完成通知
     */
    private function sendTaskCompletedNotification(AiFaceTask $task)
    {
        try {
            // 发送WebSocket通知
            event(new \App\Events\FaceSwapTaskCompleted($task));

            Log::info('发送任务完成通知', [
                'user_id' => $task->user_id,
                'batch_id' => $task->batch_id,
                'task_id' => $task->id
            ]);
            
            // 发送队列状态更新给用户
            app(\App\Services\QueueService::class)->sendQueueStatusUpdate($task->user_id);
        } catch (\Exception $e) {
            Log::error('发送任务完成通知失败', [
                'task_id' => $task->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 发送任务失败通知
     */
    private function sendTaskFailedNotification(AiFaceTask $task)
    {
        try {
            // 发送WebSocket通知
            event(new \App\Events\FaceSwapTaskFailed($task));

            Log::info('发送任务失败通知', [
                'user_id' => $task->user_id,
                'batch_id' => $task->batch_id,
                'task_id' => $task->id,
                'error' => $task->error_message
            ]);
            
            // 发送队列状态更新给用户
            app(\App\Services\QueueService::class)->sendQueueStatusUpdate($task->user_id);
        } catch (\Exception $e) {
            Log::error('发送任务失败通知失败', [
                'task_id' => $task->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 发送批次完成通知
     */
    private function sendBatchCompletedNotification(AiFaceTask $batchRecord)
    {
        try {
            // 获取批次中的所有任务结果
            $tasks = AiFaceTask::where('batch_id', $batchRecord->batch_id)
                ->where('type', 'task')
                ->get();

            $completedTasks = $tasks->where('status', 'completed');
            $failedTasks = $tasks->where('status', 'failed');

            $results = $completedTasks->map(function ($task) {
                return [
                    'task_id' => $task->id,
                    'page_id' => $task->page_id,
                    'result_image_url' => $task->result_image_url,
                    'result' => $task->result
                ];
            })->values()->toArray();

            // 发送WebSocket通知
            event(new \App\Events\FaceSwapBatchCompleted($batchRecord, $results));

            Log::info('发送批次完成通知', [
                'user_id' => $batchRecord->user_id,
                'batch_id' => $batchRecord->batch_id,
                'status' => $batchRecord->status,
                'completed_tasks' => $completedTasks->count(),
                'failed_tasks' => $failedTasks->count()
            ]);
            
            // 发送队列状态更新给用户
            app(\App\Services\QueueService::class)->sendQueueStatusUpdate($batchRecord->user_id);
        } catch (\Exception $e) {
            Log::error('发送批次完成通知失败', [
                'batch_id' => $batchRecord->batch_id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 格式化时间
     */
    private function formatTime(int $seconds)
    {
        if ($seconds < 60) {
            return $seconds . '秒';
        } elseif ($seconds < 3600) {
            $minutes = floor($seconds / 60);
            return $minutes . '分钟';
        } else {
            $hours = floor($seconds / 3600);
            $minutes = floor(($seconds % 3600) / 60);
            return $hours . '小时' . ($minutes > 0 ? $minutes . '分钟' : '');
        }
    }

    /**
     * 根据角色序列选择对应的face_image
     */
    private function selectFaceImageForCharacter(array $faceImages, array $characterSequence)
    {
        if (empty($faceImages)) {
            return ['image' => '', 'index' => 0];
        }

        if (empty($characterSequence)) {
            // 如果没有角色序列，使用第一个face_image
            return ['image' => $faceImages[0], 'index' => 0];
        }

        // 取角色序列中的第一个角色索引
        $characterIndex = $characterSequence[0] - 1; // 角色序列从1开始，数组从0开始

        // 确保索引在有效范围内
        $faceImageIndex = min($characterIndex, count($faceImages) - 1);
        $faceImageIndex = max(0, $faceImageIndex);

        return [
            'image' => $faceImages[$faceImageIndex],
            'index' => $faceImageIndex
        ];
    }
}
